@echo off 
echo Restoring environment 
set CURA_RESOURCES=
set CURA_ENGINE_SEARCH_PATH=
set "PYTHONPATH=C:\Mac\Home\Desktop\CuraProject\Cura\build\generators\cura_venv\Lib\site-packages;C:\Users\<USER>\.conan2\p\b\urani905142587c488\p\plugins;C:\Users\<USER>\.conan2\p\b\urani905142587c488\p\site-packages;C:\Users\<USER>\.conan2\p\b\pyarc5f73a3826e606\p\lib;C:\Users\<USER>\.conan2\p\b\dulci50655dc2b7370\p\lib\pyDulcificum;C:\Users\<USER>\.conan2\p\b\pysav60ff667e08561\p\lib;C:\Users\<USER>\.conan2\p\b\pynesc51129833fb96\p\lib"
set "PATH=C:\Mac\Home\Desktop\CuraProject\Cura\build\generators\cura_venv\Lib\site-packages\PyQt6\Qt6\bin;C:\Program Files\Parallels\Parallels Tools\Applications;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\STMicroelectronics\st_toolset\asm;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\CMake\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\include\cpython;c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages;C:\Program Files\STMicroelectronics\STM32Cube\STM32CubeProgrammer\bin;C:\Program Files\dotnet\x64;C:\Program Files (x86)\COSMIC\FSE_Compilers\CXSTM8;C:\Program Files (x86)\COSMIC\FSE_Compilers\CXSTM32;C:\Program Files\nodejs\;C:\gcc-arm-none-eabi-10.3-2021.10\bin;C:\OpenOCD-20240916-0.12.0\drivers;C:\OpenOCD-20240916-0.12.0\bin;C:\w64devkit\bin;C:\Program Files (x86)\STMicroelectro;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Python312-arm64\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312-arm64\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2023.3.3\bin;;c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\include\cpython;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\dotnet\x64;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\JetBrains\PyCharm 2025.1.2\bin;;;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-arm64\bundled\scripts\noConfigScripts"
set PYTHON=
set PYTHON_ROOT=
set VIRTUAL_ENV=
set LD_LIBRARY_PATH=
set DYLD_LIBRARY_PATH=
set PYTHONHOME=
