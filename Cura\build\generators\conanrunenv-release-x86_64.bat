@echo off
chcp 65001 > nul
setlocal
echo @echo off > "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
echo echo Restoring environment >> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
for %%v in (CURA_RESOURCES CURA_ENGINE_SEARCH_PATH PYTHONPATH PATH PYTHON PYTHON_ROOT) do (
    set foundenvvar=
    for /f "delims== tokens=1,2" %%a in ('set') do (
        if /I "%%a" == "%%v" (
            echo set "%%a=%%b">> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
            set foundenvvar=1
        )
    )
    if not defined foundenvvar (
        echo set %%v=>> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
    )
)
endlocal


set "CURA_RESOURCES=C:\Users\<USER>\.conan2\p\b\cura_c53e6af90401e\p\res"
set "CURA_ENGINE_SEARCH_PATH=C:\Users\<USER>\.conan2\p\b\cura_c53e6af90401e\p\res\extruders"
set "PYTHONPATH=C:\Users\<USER>\.conan2\p\b\urani905142587c488\p\plugins;C:\Users\<USER>\.conan2\p\b\urani905142587c488\p\site-packages;C:\Users\<USER>\.conan2\p\b\pyarc5f73a3826e606\p\lib;C:\Users\<USER>\.conan2\p\b\dulci50655dc2b7370\p\lib\pyDulcificum;C:\Users\<USER>\.conan2\p\b\pysav60ff667e08561\p\lib;C:\Users\<USER>\.conan2\p\b\pynesc51129833fb96\p\lib;%PYTHONPATH%"
set "PATH=%~dp0\..\..\venv\Lib\site-packages\PyQt6\Qt6\bin;C:\Users\<USER>\.conan2\p\b\curae3072c54810b88\p\bin;C:\Users\<USER>\.conan2\p\b\arcusf00e1e4633b02\p\bin;C:\Users\<USER>\.conan2\p\b\savitba5a3c8dca2ce\p\bin;C:\Users\<USER>\.conan2\p\b\nest29b6fd26283806\p\bin;C:\Users\<USER>\.conan2\p\nlopt303d2f3e60ce9\p\bin;C:\Users\<USER>\.conan2\p\b\clippc283f5b8659ec\p\bin;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p\bin;%PATH%;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p\bin;windows\arduino\amd64;windows\arduino\CP210x_6.7.4;windows\arduino\FTDI USB Drivers\amd64"
set "PYTHON=%PYTHON%;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p\bin\python.exe"
set "PYTHON_ROOT=%PYTHON_ROOT%;C:\Users\<USER>\.conan2\p\b\cpytha018a1def4567\p"